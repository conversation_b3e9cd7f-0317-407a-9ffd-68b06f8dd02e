import 'package:dartz/dartz.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/offer.dart';
import '../../domain/entities/platform.dart';
import '../../domain/entities/product.dart';
import '../../domain/repositories/home_repository.dart';
import '../datasources/home_local_data_source.dart';
import '../datasources/home_remote_data_source.dart';

class HomeRepositoryImpl implements HomeRepository {
  final HomeRemoteDataSource remoteDataSource;
  final HomeLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  HomeRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<Offer>>> getFeaturedOffers() async {
    if (await networkInfo.isConnected) {
      try {
        final remoteOffers = await remoteDataSource.getOffers();

        // If Supabase returns empty results, fall back to local data for demo purposes
        if (remoteOffers.isEmpty) {
          final localOffers = await localDataSource.getFeaturedOffers();
          return Right(localOffers);
        }

        return Right(remoteOffers);
      } on ServerException catch (e) {
        // If remote fails, try local fallback
        try {
          final localOffers = await localDataSource.getFeaturedOffers();
          return Right(localOffers);
        } on CacheException {
          return Left(ServerFailure(message: e.message));
        }
      } catch (e) {
        // If remote fails, try local fallback
        try {
          final localOffers = await localDataSource.getFeaturedOffers();
          return Right(localOffers);
        } on CacheException {
          return Left(ServerFailure(message: 'Remote failed: $e'));
        }
      }
    } else {
      // No internet connection, use local data
      try {
        final localOffers = await localDataSource.getFeaturedOffers();
        return Right(localOffers);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      } catch (e) {
        return Left(
          CacheFailure(message: 'Failed to get cached offers: ${e.toString()}'),
        );
      }
    }
  }

  @override
  Future<Either<Failure, List<Platform>>> getPlatforms() async {
    try {
      final platforms = await localDataSource.getPlatforms();
      return Right(platforms);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to get platforms: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<Platform>>> getPlatformsByType(
    String type,
  ) async {
    try {
      final platforms = await localDataSource.getPlatformsByType(type);
      return Right(platforms);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to get platforms by type: ${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, List<Product>>> getSuggestedProducts() async {
    try {
      final products = await localDataSource.getSuggestedProducts();
      return Right(products);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to get suggested products: ${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, List<Product>>> searchProducts(String query) async {
    try {
      final products = await localDataSource.searchProducts(query);
      return Right(products);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to search products: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<Product>>> searchProductsByImage(
    String imagePath,
  ) async {
    // Placeholder implementation for image search
    try {
      // For now, return suggested products as placeholder
      final products = await localDataSource.getSuggestedProducts();
      return Right(products);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to search products by image: ${e.toString()}',
        ),
      );
    }
  }
}
